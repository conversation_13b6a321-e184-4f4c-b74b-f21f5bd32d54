<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <style>
    body {
      margin: 0;
      padding: 0;
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 250px;
      height: 250px;
      overflow: hidden;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .loader-container {
      width: 250px;
      height: 250px;
      background: #0f0f23;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.24);
      position: relative;
    }

    .loader-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      padding-bottom: 20px;
    }

    .loader-gif {
      width: 200px;
      height: 200px;
      object-fit: contain;
    }

    .progress-container {
      width: 200px;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
      overflow: hidden;
      margin-top: 8px;
      opacity: 1 !important;
      visibility: visible !important;
      position: relative;
      z-index: 1000;
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #4f46e5, #7c3aed);
      border-radius: 2px;
      width: 0%;
      transition: width 0.3s ease;
      animation: progress-pulse 2s ease-in-out infinite;
      opacity: 1 !important;
      visibility: visible !important;
      position: relative;
      z-index: 1001;
    }

    .progress-text {
      color: #e2e8f0;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      opacity: 0.8 !important;
      visibility: visible !important;
      position: relative;
      z-index: 1000;
    }

    @keyframes progress-pulse {

      0%,
      100% {
        opacity: 0.8;
      }

      50% {
        opacity: 1;
      }
    }
  </style>
</head>

<body>
  <div class="loader-container">
    <div class="loader-content">
      <img src="./loading.gif" class="loader-gif" alt="Loading..." />
      <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
      </div>
      <div class="progress-text" id="progressText">Starting MoeDownloader...</div>
    </div>
  </div>

  <script>
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    // Default fallback progress steps
    const defaultSteps = [
      { text: 'Starting MoeDownloader...', duration: 500 },
      { text: 'Initializing database...', duration: 800 },
      { text: 'Loading services...', duration: 1000 },
      { text: 'Setting up RSS processor...', duration: 700 },
      { text: 'Configuring AniList...', duration: 600 },
      { text: 'Preparing download manager...', duration: 500 },
      { text: 'Loading user interface...', duration: 400 },
      { text: 'Almost ready...', duration: 300 }
    ];

    let currentStep = 0;
    let usingRealProgress = false;

    // Global function for main process to update progress
    window.updateProgress = function (text, progress) {
      usingRealProgress = true;
      progressText.textContent = text;
      progressBar.style.width = Math.min(100, Math.max(0, progress)) + '%';

      // Keep progress visible and maintain final state
      if (progress >= 100) {
        // Keep the progress bar at 100% and show final message
        progressBar.style.width = '100%';
      }
    };

    // Fallback progress animation if main process doesn't update
    function fallbackProgress() {
      if (!usingRealProgress && currentStep < defaultSteps.length) {
        const step = defaultSteps[currentStep];
        progressText.textContent = step.text;

        const targetProgress = ((currentStep + 1) / defaultSteps.length) * 100;
        progressBar.style.width = targetProgress + '%';

        // Keep final progress visible
        if (currentStep === defaultSteps.length - 1) {
          progressBar.style.width = '100%';
          progressText.textContent = 'Almost ready...';
        }

        setTimeout(() => {
          currentStep++;
          fallbackProgress();
        }, step.duration);
      }
    }

    // Start fallback progress animation
    setTimeout(fallbackProgress, 200);

    // Stop fallback after 10 seconds to avoid conflicts
    setTimeout(() => {
      if (!usingRealProgress) {
        usingRealProgress = true;
      }
    }, 10000);
  </script>
</body>

</html>